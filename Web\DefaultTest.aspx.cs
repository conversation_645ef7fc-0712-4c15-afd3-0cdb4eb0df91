using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Telerik.Web.UI;
using TrackResults.BES.Data;
using TrackResults.BES.Data.Business;
using TrackResults.BES.Data.Cache;
using TrackResults.BES.Data.Cache.Static;
using TrackResults.BES.Data.Criteria.TierOne;
using TrackResults.BES.Data.Tables.Reports;
using TrackResults.BES.Data.Types;
using TrackResults.BES.DataAccess;
using TrackResults.BES.DataAccess.Reports;
using TrackResults.BES.DataAccess.Secure;
using TrackResults.BES.Keys;
using TrackResults.BES.Services;
using TrackResults.Common.Core.Data;
using TrackResults.Common.Core.Display;
using TrackResults.Common.Core.Web;
using TrackResults.Common.DAL;
using TrackResults.Common.Utilities;
using TrackResults.Common.Web.Globalization;
using TrackResults.Web.Services;

namespace TrackResults.Web
{
	public partial class DefaultTest : PageBase
	{
		private static Dictionary<string, string> dateRanges = new Dictionary<string, string>();
		private static Dictionary<string, string> yearRanges = new Dictionary<string, string>();

		static DefaultTest()
		{
			dateRanges.Add(((int)DateRangePeriod.Today).ToString(), DateRangePeriod.Today.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.Tomorrow).ToString(), DateRangePeriod.Tomorrow.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.Yesterday).ToString(), DateRangePeriod.Yesterday.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.LastSevenDays).ToString(), DateRangePeriod.LastSevenDays.GetDisplayName());
            dateRanges.Add(((int)DateRangePeriod.NextSevenDays).ToString(), DateRangePeriod.NextSevenDays.GetDisplayName());
            dateRanges.Add(((int)DateRangePeriod.WeekToDate).ToString(), DateRangePeriod.WeekToDate.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.CurrentWeek).ToString(), DateRangePeriod.CurrentWeek.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.NextWeek).ToString(), DateRangePeriod.NextWeek.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.LastWeek).ToString(), DateRangePeriod.LastWeek.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.LastThirtyDays).ToString(), DateRangePeriod.LastThirtyDays.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.LastSixtyDays).ToString(), DateRangePeriod.LastSixtyDays.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.MonthToDate).ToString(), DateRangePeriod.MonthToDate.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.CurrentMonth).ToString(), DateRangePeriod.CurrentMonth.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.NextMonth).ToString(), DateRangePeriod.NextMonth.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.LastMonth).ToString(), DateRangePeriod.LastMonth.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.QuarterToDate).ToString(), DateRangePeriod.QuarterToDate.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.LastQuarter).ToString(), DateRangePeriod.LastQuarter.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.YearToDate).ToString(), DateRangePeriod.YearToDate.GetDisplayName());
			dateRanges.Add(((int)DateRangePeriod.CurrentYear).ToString(), DateRangePeriod.CurrentYear.GetDisplayName());

			yearRanges.Add(((int)YearRangePeriod.CurrentYear).ToString(), YearRangePeriod.CurrentYear.GetDisplayName());
			yearRanges.Add(((int)YearRangePeriod.LastYear).ToString(), YearRangePeriod.LastYear.GetDisplayName());
			yearRanges.Add(((int)YearRangePeriod.TwoYearsAgo).ToString(), YearRangePeriod.TwoYearsAgo.GetDisplayName());
			yearRanges.Add(((int)YearRangePeriod.ThreeYearsAgo).ToString(), YearRangePeriod.ThreeYearsAgo.GetDisplayName());
            yearRanges.Add(((int)YearRangePeriod.FourYearsAgo).ToString(), YearRangePeriod.FourYearsAgo.GetDisplayName());
            yearRanges.Add(((int)YearRangePeriod.FiveYearsAgo).ToString(), YearRangePeriod.FiveYearsAgo.GetDisplayName());
		}

		protected override void OnLoad(EventArgs e)
		{
           
            base.OnLoad(e);

			if (ApplicationSettingsService.DeploymentHomePageUrl != null)
			{
				Response.Redirect(ApplicationSettingsService.DeploymentHomePageUrl);
			}
			else
			{
				if (!IsPostBack)
				{
					if (FeaturesCache.HasFeature("Report.HomePage"))
					{
						BindReports(ApplicationSettingsService.DefaultDashboardDateRange, YearRangePeriod.CurrentYear);

						dropDownDateRanges.DataSource = dateRanges;
						dropDownDateRanges.DataBind();

						dropDownDateRanges.SelectedValue = ((int)ApplicationSettingsService.DefaultDashboardDateRange).ToString();

						dropDownYearRanges.DataSource = yearRanges;
						dropDownYearRanges.DataBind();

						dropDownYearRanges.SelectedValue = ((int)YearRangePeriod.CurrentYear).ToString();
					}
				}
			}
		}

		protected void dropDownDateRanges_SelectedIndexChanged(object sender, EventArgs e)
		{
			BindReports((DateRangePeriod)int.Parse(dropDownDateRanges.SelectedValue),
				(YearRangePeriod)int.Parse(dropDownYearRanges.SelectedValue));
		}

		protected void dropDownYearRanges_SelectedIndexChanged(object sender, EventArgs e)
		{
			BindReports((DateRangePeriod)int.Parse(dropDownDateRanges.SelectedValue),
				(YearRangePeriod)int.Parse(dropDownYearRanges.SelectedValue));
		}

		protected void dropDownSalesLine_SelectedIndexChanged(object sender, EventArgs e)
		{
			BindReports((DateRangePeriod)int.Parse(dropDownDateRanges.SelectedValue),
				(YearRangePeriod)int.Parse(dropDownYearRanges.SelectedValue));
		}

		private void BindReports(DateRangePeriod dateRangePeriod, YearRangePeriod yearRangePeriod)
		{

            TierOneCriteria tierOneCriteria = new TierOneCriteria();
			TierOneCriteria tierOneCriteriaDefault = new TierOneCriteria();
			var DateTimeCriteria = tierOneCriteria.DateTimeCriteria;
			tierOneCriteriaDefault = CustomAnalyticViewsDataAccess.SelectCriteria<TierOneCriteria>("cqwq9w");
			tierOneCriteria = tierOneCriteriaDefault;
			tierOneCriteria.DateTimeCriteria = DateTimeCriteria;

			tierOneCriteria.DateTimeCriteria.TierOneDateTypeID = (int)TierOneDateType.SaleDate;
			tierOneCriteria.DateTimeCriteria.DateRangePeriodID = (int)dateRangePeriod;

			if (yearRangePeriod != YearRangePeriod.CurrentYear)
			{
				tierOneCriteria.DateTimeCriteria.YearRangePeriodID = (int)yearRangePeriod;
			}

			// Marketing reports
			gridViewManifest.DataSource = GetDaysManifestReport(null);
			gridViewManifest.DataBind();

			DataTable efficiencyMarketingReport = GetEfficiencyMarketingReport(tierOneCriteria, tierOneCriteria.BasicCriteria.ReportCalculateSettings);

			gridViewTours.DataSource = efficiencyMarketingReport;
			gridViewTours.DataBind();

			gridViewShow.DataSource = efficiencyMarketingReport;
			gridViewShow.DataBind();

			// Sales reports
			DataTable salesLocationsEfficiencyReport = GetEfficiencySalesReport(tierOneCriteria, tierOneCriteria.BasicCriteria.ReportCalculateSettings);

			gridViewNetTotals.DataSource = salesLocationsEfficiencyReport;
			gridViewNetTotals.DataBind();

			gridViewNetVolume.DataSource = salesLocationsEfficiencyReport;
			gridViewNetVolume.DataBind();

			gridViewCancels.DataSource = GetStatusSalesReport(tierOneCriteria);
			gridViewCancels.DataBind();

			gridViewCancellationRequests.DataSource = GetCancellationRequestsStatusReport(tierOneCriteria);
			gridViewCancellationRequests.DataBind();

			// Populate KPI cards
			PopulateKPICards(efficiencyMarketingReport, salesLocationsEfficiencyReport);

			// Populate charts
			PopulateCharts(tierOneCriteria, efficiencyMarketingReport, salesLocationsEfficiencyReport);
		}

		private void PopulateKPICards(DataTable marketingReport, DataTable salesReport)
		{
			try
			{
				if (marketingReport != null && marketingReport.Rows.Count > 0)
				{
					var summaryRow = marketingReport.Rows[0];
					
					// Total Tours
					if (summaryRow["qualified"] != DBNull.Value)
					{
						lblTotalTours.Text = Convert.ToInt32(summaryRow["qualified"]).ToString("N0");
					}
				}

				if (salesReport != null && salesReport.Rows.Count > 0)
				{
					var summaryRow = salesReport.Rows[0];
					
					// Net Sales
					if (summaryRow["netSales"] != DBNull.Value)
					{
						lblNetSales.Text = Convert.ToInt32(summaryRow["netSales"]).ToString("N0");
					}

					// Close Rate
					if (summaryRow["netClosePercent"] != DBNull.Value)
					{
						lblCloseRate.Text = Convert.ToDecimal(summaryRow["netClosePercent"]).ToString("F1") + "%";
					}

					// Net Volume
					if (summaryRow["netVolume"] != DBNull.Value)
					{
						lblNetVolume.Text = Convert.ToDecimal(summaryRow["netVolume"]).ToString("C0");
					}
				}

				// Add trend indicators (placeholder for now)
				lblToursChange.Text = "↗ +5.2%";
				lblSalesChange.Text = "↗ +8.1%";
				lblCloseRateChange.Text = "↗ +2.3%";
				lblVolumeChange.Text = "↗ +12.5%";
			}
			catch (Exception ex)
			{
				// Log error and set default values
				lblTotalTours.Text = "0";
				lblNetSales.Text = "0";
				lblCloseRate.Text = "0%";
				lblNetVolume.Text = "$0";
			}
		}

		private void PopulateCharts(TierOneCriteria tierOneCriteria, DataTable marketingReport, DataTable salesReport)
		{
			try
			{
				// Populate Sales Trend Chart
				PopulateSalesTrendChart(tierOneCriteria);

				// Populate Tour Status Chart
				PopulateTourStatusChart(marketingReport);

				// Populate Volume Analysis Chart
				PopulateVolumeAnalysisChart(salesReport);

				// Populate Efficiency Chart
				PopulateEfficiencyChart(marketingReport, salesReport);
			}
			catch (Exception ex)
			{
				// Log error - charts will remain empty
			}
		}

		private void PopulateSalesTrendChart(TierOneCriteria tierOneCriteria)
		{
			try
			{
				// Get historical data for trend analysis
				var salesData = GetEfficiencySalesReport(tierOneCriteria, tierOneCriteria.BasicCriteria.ReportCalculateSettings);
				
				chartSalesTrend.PlotArea.XAxis.Items.Clear();
				((Telerik.Web.UI.LineSeries)chartSalesTrend.PlotArea.Series[0]).SeriesItems.Clear();

				if (salesData != null && salesData.Rows.Count > 0)
				{
					foreach (DataRow row in salesData.Rows)
					{
						if (row["name"].ToString() != "Summary")
						{
							chartSalesTrend.PlotArea.XAxis.Items.Add(row["name"].ToString());
							
							decimal netSales = 0;
							if (row["netSales"] != DBNull.Value)
							{
								netSales = Convert.ToDecimal(row["netSales"]);
							}
							
							((Telerik.Web.UI.LineSeries)chartSalesTrend.PlotArea.Series[0]).SeriesItems.Add(netSales);
						}
					}
				}
			}
			catch (Exception ex)
			{
				// Log error
			}
		}

		private void PopulateTourStatusChart(DataTable marketingReport)
		{
			try
			{
				((Telerik.Web.UI.DonutSeries)chartTourStatus.PlotArea.Series[0]).SeriesItems.Clear();

				if (marketingReport != null && marketingReport.Rows.Count > 0)
				{
					var summaryRow = marketingReport.Rows[0];
					
					if (summaryRow["qualified"] != DBNull.Value)
					{
						((Telerik.Web.UI.DonutSeries)chartTourStatus.PlotArea.Series[0]).SeriesItems.Add(
							new PieSeriesItem(Convert.ToDecimal(summaryRow["qualified"]), "Qualified"));
					}

					if (summaryRow["courtesyTour"] != DBNull.Value)
					{
						((Telerik.Web.UI.DonutSeries)chartTourStatus.PlotArea.Series[0]).SeriesItems.Add(
							new PieSeriesItem(Convert.ToDecimal(summaryRow["courtesyTour"]), "Courtesy"));
					}

					if (summaryRow["notQualified"] != DBNull.Value)
					{
						((Telerik.Web.UI.DonutSeries)chartTourStatus.PlotArea.Series[0]).SeriesItems.Add(
							new PieSeriesItem(Convert.ToDecimal(summaryRow["notQualified"]), "Not Qualified"));
					}
				}
			}
			catch (Exception ex)
			{
				// Log error
			}
		}

		private void PopulateVolumeAnalysisChart(DataTable salesReport)
		{
			try
			{
				chartVolumeAnalysis.PlotArea.XAxis.Items.Clear();
				((Telerik.Web.UI.ColumnSeries)chartVolumeAnalysis.PlotArea.Series[0]).SeriesItems.Clear();
				((Telerik.Web.UI.ColumnSeries)chartVolumeAnalysis.PlotArea.Series[1]).SeriesItems.Clear();

				if (salesReport != null && salesReport.Rows.Count > 0)
				{
					foreach (DataRow row in salesReport.Rows)
					{
						if (row["name"].ToString() != "Summary")
						{
							chartVolumeAnalysis.PlotArea.XAxis.Items.Add(row["name"].ToString());
							
							decimal cashVolume = 0;
							decimal totalVolume = 0;
							
							if (row["cashVolume"] != DBNull.Value)
							{
								cashVolume = Convert.ToDecimal(row["cashVolume"]);
							}
							
							if (row["netVolume"] != DBNull.Value)
							{
								totalVolume = Convert.ToDecimal(row["netVolume"]);
							}
							
							decimal creditVolume = totalVolume - cashVolume;
							
							((Telerik.Web.UI.ColumnSeries)chartVolumeAnalysis.PlotArea.Series[0]).SeriesItems.Add(cashVolume);
							((Telerik.Web.UI.ColumnSeries)chartVolumeAnalysis.PlotArea.Series[1]).SeriesItems.Add(creditVolume);
						}
					}
				}
			}
			catch (Exception ex)
			{
				// Log error
			}
		}

		private void PopulateEfficiencyChart(DataTable marketingReport, DataTable salesReport)
		{
			try
			{
				chartEfficiency.PlotArea.XAxis.Items.Clear();
				((Telerik.Web.UI.BarSeries)chartEfficiency.PlotArea.Series[0]).SeriesItems.Clear();
				((Telerik.Web.UI.BarSeries)chartEfficiency.PlotArea.Series[1]).SeriesItems.Clear();

				if (marketingReport != null && salesReport != null && 
					marketingReport.Rows.Count > 0 && salesReport.Rows.Count > 0)
				{
					// Combine data from both reports
					for (int i = 0; i < Math.Min(marketingReport.Rows.Count, salesReport.Rows.Count); i++)
					{
						var marketingRow = marketingReport.Rows[i];
						var salesRow = salesReport.Rows[i];
						
						if (marketingRow["name"].ToString() != "Summary")
						{
							chartEfficiency.PlotArea.XAxis.Items.Add(marketingRow["name"].ToString());
							
							decimal showRate = 0;
							decimal closeRate = 0;
							
							if (marketingRow["netShowPercent"] != DBNull.Value)
							{
								showRate = Convert.ToDecimal(marketingRow["netShowPercent"]);
							}
							
							if (salesRow["netClosePercent"] != DBNull.Value)
							{
								closeRate = Convert.ToDecimal(salesRow["netClosePercent"]);
							}
							
							((Telerik.Web.UI.BarSeries)chartEfficiency.PlotArea.Series[0]).SeriesItems.Add(showRate);
							((Telerik.Web.UI.BarSeries)chartEfficiency.PlotArea.Series[1]).SeriesItems.Add(closeRate);
						}
					}
				}
			}
			catch (Exception ex)
			{
				// Log error
			}
		}

		// Reuse existing methods from Default.aspx.cs
		public List<DataRow> GetDaysManifestReport(HashSet<string> reportCalculateSettings)
		{
			TierOneCriteria tierOneCriteria = new TierOneCriteria();

			DaysManifestDataAccess daysManifestDataAccess = new DaysManifestDataAccess();
			DataSet dataSet = daysManifestDataAccess.SelectReport(tierOneCriteria, ReportByType.Tour_LocationID, null, reportCalculateSettings);

			List<DataRow> reportRows = new List<DataRow>(dataSet.Tables[0].Select(null, "name ASC"));
			dataSet.Tables[Constants.SUMMARY_TABLE].Rows[0]["name"] = "Summary";
			reportRows.Insert(0, dataSet.Tables[Constants.SUMMARY_TABLE].Rows[0]);

			return reportRows;
		}

		public DataTable GetEfficiencyMarketingReport(TierOneCriteria tierOneCriteria, HashSet<string> reportCalculateSettings)
		{
			ToursReportByBaseDataAccess efficiencyMarketingDataAccess =
				CustomAnalyticViewsService.GetReportByDataAccess(ApplicationViewKeys.MarketingEfficiencyView, ReportByType.Tour_LocationID);

			DataSet dataSet = efficiencyMarketingDataAccess.SelectReport(tierOneCriteria, ReportByType.Tour_LocationID, null, reportCalculateSettings);
			List<DataRow> sortedReportRows = new List<DataRow>(dataSet.Tables[0].Select(null, "name ASC"));

			DataTable reportTable = dataSet.Tables[Constants.SUMMARY_TABLE];
			reportTable.Rows[0]["name"] = "Summary";

			foreach (DataRow row in sortedReportRows)
			{
				reportTable.ImportRow(row);
			}

			return reportTable;
		}

		public DataTable GetEfficiencySalesReport(TierOneCriteria tierOneCriteria, HashSet<string> reportCalculateSettings)
		{
			ToursReportByBaseDataAccess efficiencySalesDataAccess =
				CustomAnalyticViewsService.GetReportByDataAccess(ApplicationViewKeys.SalesEfficiencyView, ReportByType.Tour_LocationID);

			DataSet dataSet = efficiencySalesDataAccess.SelectReport(tierOneCriteria, ReportByType.Tour_LocationID, null, reportCalculateSettings);
			List<DataRow> sortedReportRows = new List<DataRow>(dataSet.Tables[0].Select(null, "name ASC"));

			DataTable reportTable = dataSet.Tables[Constants.SUMMARY_TABLE];
			reportTable.Rows[0]["name"] = "Summary";

			foreach (DataRow row in sortedReportRows)
			{
				reportTable.ImportRow(row);
			}

			return reportTable;
		}

		public List<DataRow> GetStatusSalesReport(TierOneCriteria tierOneCriteria)
		{
			StatusSalesDataAccess statusSalesDataAccess = new StatusSalesDataAccess();
			DataSet dataSet = statusSalesDataAccess.SelectReport(tierOneCriteria, ReportByType.Tour_LocationID, null, null);

			dataSet.Tables[Constants.SUMMARY_TABLE].Rows[0]["name"] = "Summary";
			List<DataRow> reportRows = new List<DataRow>(dataSet.Tables[0].Select(null, "name ASC"));
			reportRows.Insert(0, dataSet.Tables[Constants.SUMMARY_TABLE].Rows[0]);

			return reportRows;
		}

		public List<DataRow> GetCancellationRequestsStatusReport(TierOneCriteria tierOneCriteria)
		{
			CancellationRequestStatusDataAccess cancellationRequestStatusDataAccess = new CancellationRequestStatusDataAccess();
			DataSet dataSet = cancellationRequestStatusDataAccess.SelectReport(tierOneCriteria, ReportByType.Tour_LocationID, null);

			dataSet.Tables[Constants.SUMMARY_TABLE].Rows[0]["name"] = "Summary";
			List<DataRow> reportRows = new List<DataRow>(dataSet.Tables[0].Select(null, "name ASC"));
			reportRows.Insert(0, dataSet.Tables[Constants.SUMMARY_TABLE].Rows[0]);

			return reportRows;
		}
	}
}
