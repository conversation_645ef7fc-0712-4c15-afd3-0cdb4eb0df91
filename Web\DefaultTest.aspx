<%@ Page Title="Enhanced Dashboard" Language="C#" MasterPageFile="~/MasterPages/Default.Master" CodeBehind="Default.aspx.cs" Inherits="TrackResults.Web.Default" %>
<%@ Register TagPrefix="telerik" Namespace="Telerik.Web.UI" Assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="c1" runat="server">

	<trwc:FeaturePlaceHolder runat="server" FeatureKey="Report.HomePage">

	<div class="band band-white-space-small gray-lighter-background">
	<div class="container">
	<div class="row">
	<div class="col-xs-offset-6 col-xs-6">

		Dates
		<asp:DropDownList ID="dropDownDateRanges" runat="server" DataTextField="Value" DataValueField="Key" AutoPostBack="true" CssClass="input-inline input-medium-height"
			OnSelectedIndexChanged="dropDownDateRanges_SelectedIndexChanged" />
		<asp:DropDownList ID="dropDownYearRanges" runat="server" DataValueField="Key" DataTextField="Value" AutoPostBack="true" CssClass="input-inline input-medium-width"
			OnSelectedIndexChanged="dropDownYearRanges_SelectedIndexChanged" />

		<trwc:FeaturePlaceHolder runat="server" FeatureKey="SalesLine">
			<span style="margin-left:5px;">Line:</span>
			<asp:DropDownList ID="dropDownSalesLine" runat="server" AutoPostBack="true" OnSelectedIndexChanged="dropDownSalesLine_SelectedIndexChanged">
				<asp:ListItem Text="All" Value="" Selected="true" />
				<asp:ListItem Text="Frontline" Value="1" />
				<asp:ListItem Text="Owner" Value="2" />
				<asp:ListItem Text="Exit" Value="3" />
			</asp:DropDownList>
		</trwc:FeaturePlaceHolder>

	</div>
	</div>
	</div>
	</div>

	<!-- Enhanced KPI Summary Cards -->
	<div class="band band-white-space-small">
	<div class="container">
		<div class="row">
			<div class="col-xs-12">
				<h3 style="color: #2c3e50; margin-bottom: 20px; font-weight: 600;">📊 Key Performance Indicators</h3>
			</div>
		</div>
		<div class="row" id="kpiCards">
			<div class="col-xs-3">
				<div class="panel panel-primary kpi-card" style="border-color: #3498db;">
					<div class="panel-body text-center" style="background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 20px;">
						<h4 style="margin: 0; font-size: 2.2em; font-weight: bold;" id="totalToursValue">
							Loading...
						</h4>
						<p style="margin: 5px 0 0 0; font-size: 0.9em; opacity: 0.9;">Total Tours</p>
						<small style="font-size: 0.8em; opacity: 0.8;">
							↗ +5.2%
						</small>
					</div>
				</div>
			</div>
			<div class="col-xs-3">
				<div class="panel panel-success kpi-card" style="border-color: #27ae60;">
					<div class="panel-body text-center" style="background: linear-gradient(135deg, #27ae60, #229954); color: white; padding: 20px;">
						<h4 style="margin: 0; font-size: 2.2em; font-weight: bold;" id="netSalesValue">
							Loading...
						</h4>
						<p style="margin: 5px 0 0 0; font-size: 0.9em; opacity: 0.9;">Net Sales</p>
						<small style="font-size: 0.8em; opacity: 0.8;">
							↗ +8.1%
						</small>
					</div>
				</div>
			</div>
			<div class="col-xs-3">
				<div class="panel panel-warning kpi-card" style="border-color: #f39c12;">
					<div class="panel-body text-center" style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white; padding: 20px;">
						<h4 style="margin: 0; font-size: 2.2em; font-weight: bold;" id="closeRateValue">
							Loading...
						</h4>
						<p style="margin: 5px 0 0 0; font-size: 0.9em; opacity: 0.9;">Close Rate</p>
						<small style="font-size: 0.8em; opacity: 0.8;">
							↗ +2.3%
						</small>
					</div>
				</div>
			</div>
			<div class="col-xs-3">
				<div class="panel panel-info kpi-card" style="border-color: #8e44ad;">
					<div class="panel-body text-center" style="background: linear-gradient(135deg, #8e44ad, #7d3c98); color: white; padding: 20px;">
						<h4 style="margin: 0; font-size: 2.2em; font-weight: bold;" id="netVolumeValue">
							Loading...
						</h4>
						<p style="margin: 5px 0 0 0; font-size: 0.9em; opacity: 0.9;">Net Volume</p>
						<small style="font-size: 0.8em; opacity: 0.8;">
							↗ +12.5%
						</small>
					</div>
				</div>
			</div>
		</div>
	</div>
	</div>

	<!-- Performance Summary Section -->
	<div class="band band-white-space">
	<div class="container">
		<div class="row">
			<div class="col-xs-12">
				<h3 style="color: #2c3e50; margin-bottom: 20px; font-weight: 600;">📈 Performance Analytics</h3>
			</div>
		</div>
		<div class="row">
			<div class="col-xs-6">
				<div class="panel panel-default">
					<div class="panel-heading" style="background-color: #34495e; color: white;">
						<h4 style="margin: 0;">📊 Quick Analytics</h4>
					</div>
					<div class="panel-body" style="padding: 20px;">
						<div class="row">
							<div class="col-xs-6">
								<div class="text-center" style="padding: 15px; background-color: #ecf0f1; border-radius: 5px; margin-bottom: 10px;">
									<h5 style="margin: 0; color: #2c3e50;">Show Rate</h5>
									<h3 style="margin: 5px 0; color: #27ae60;" id="showRateDisplay">85.5%</h3>
									<small style="color: #7f8c8d;">↗ +2.1%</small>
								</div>
							</div>
							<div class="col-xs-6">
								<div class="text-center" style="padding: 15px; background-color: #ecf0f1; border-radius: 5px; margin-bottom: 10px;">
									<h5 style="margin: 0; color: #2c3e50;">Avg Deal Size</h5>
									<h3 style="margin: 5px 0; color: #3498db;" id="avgDealDisplay">$4,136</h3>
									<small style="color: #7f8c8d;">↗ +5.8%</small>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-xs-6">
								<div class="text-center" style="padding: 15px; background-color: #ecf0f1; border-radius: 5px;">
									<h5 style="margin: 0; color: #2c3e50;">Cash %</h5>
									<h3 style="margin: 5px 0; color: #f39c12;" id="cashPercentDisplay">35.2%</h3>
									<small style="color: #7f8c8d;">↘ -1.2%</small>
								</div>
							</div>
							<div class="col-xs-6">
								<div class="text-center" style="padding: 15px; background-color: #ecf0f1; border-radius: 5px;">
									<h5 style="margin: 0; color: #2c3e50;">VPG</h5>
									<h3 style="margin: 5px 0; color: #9b59b6;" id="vpgDisplay">$1,901</h3>
									<small style="color: #7f8c8d;">↗ +3.4%</small>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="col-xs-6">
				<div class="panel panel-default">
					<div class="panel-heading" style="background-color: #34495e; color: white;">
						<h4 style="margin: 0;">🎯 Action Items Summary</h4>
					</div>
					<div class="panel-body" style="padding: 20px;">
						<div class="alert alert-warning" style="margin-bottom: 15px;">
							<strong>⚠️ Attention Required:</strong>
							<ul style="margin: 10px 0 0 0;">
								<li><span id="pendingCount">8</span> Pending items requiring review</li>
								<li><span id="canceledCount">12</span> Canceled items this period</li>
								<li><span id="creditConcernCount">3</span> Credit concerns to address</li>
							</ul>
						</div>
						<div class="alert alert-info" style="margin-bottom: 0;">
							<strong>📋 Cancellation Requests:</strong>
							<ul style="margin: 10px 0 0 0;">
								<li><span id="unassignedCount">5</span> Unassigned requests</li>
								<li><span id="inProgressCount">8</span> In progress</li>
								<li><span id="savedCount">15</span> Successfully saved</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	</div>

	<!-- Enhanced Data Tables Section -->
	<div class="band band-white-space">
	<div class="container">	
	
		<div class="row">
		<div class="col-xs-6">
			
			<div class="panel panel-default">
				<div class="panel-heading" style="background-color: #2980b9; color: white;">
					<h4 style="margin: 0;">Marketing Performance</h4>
				</div>
				<div class="panel-body">
				
					<trwc:ExtendedGridView ID="gridViewManifest" runat="server" SkinID="reportSummary">
						<Columns>
							<asp:BoundField DataField="name" HeaderText="Manifest" />
							<asp:BoundField DataField="today" HeaderText="Today" />
							<asp:BoundField DataField="tomorrow" HeaderText="Tomorrow" />
							<asp:BoundField DataField="dayAfterTomorrow" HeaderText="Day After" />
							<asp:BoundField DataField="total" HeaderText="Total" />
						</Columns>
					</trwc:ExtendedGridView>

				</div>
			</div>

			<div class="panel panel-default">
				<div class="panel-heading" style="background-color: #27ae60; color: white;">
					<h4 style="margin: 0;">Tour Analysis</h4>
				</div>
				<div class="panel-body">

					<trwc:ExtendedGridView ID="gridViewTours" runat="server" SkinID="reportSummary">
						<Columns>
							<asp:BoundField DataField="name" HeaderText="Tours" />
							<asp:BoundField DataField="qualified" HeaderText='<%$ TourStatusTypeCode:Qualified %>' />
							<asp:BoundField AccessibleHeaderText="id_courtesyTour" DataField="courtesyTour" HeaderText='<%$ TourStatusTypeCode:CourtesyTour %>' />
							<asp:BoundField AccessibleHeaderText="id_courtesyTourPercent" DataField="courtesyTourPercent" HeaderText='<%$ TourStatusTypeCodePercent:CourtesyTour %>'
								SortExpression="courtesyTourPercent" DataFormatString="{0:F0}%" HtmlEncode="False" />
							<asp:BoundField DataField="notQualified" HeaderText='<%$ TourStatusTypeCode:NotQualified %>' />
							<asp:BoundField DataField="notQualifiedPercent" HeaderText='<%$ TourStatusTypeCodePercent:NotQualified %>'
								SortExpression="notQualifiedPercent" DataFormatString="{0:F0}%" HtmlEncode="False" />
							<asp:TemplateField Visible="false" HeaderText="ColumnDisplayRights">
								<itemtemplate>
									<asp:PlaceHolder ID="courtesyTourColumn" runat="server" />
									<asp:PlaceHolder ID="courtesyTourPercent" runat="server" />
								</itemtemplate>
							</asp:TemplateField>
						</Columns>
					</trwc:ExtendedGridView>

				</div>
			</div>

			<div class="panel panel-default">
				<div class="panel-heading" style="background-color: #8e44ad; color: white;">
					<h4 style="margin: 0;">Showing Performance</h4>
				</div>
				<div class="panel-body">

					<trwc:ExtendedGridView ID="gridViewShow" runat="server" SkinID="reportSummary">
						<Columns>
							<asp:BoundField DataField="name" HeaderText="Showings" />
							<asp:BoundField DataField="noShow" HeaderText='<%$ TourStatusTypeName:NoShow %>' />
							<asp:BoundField DataField="qualifiedShowPercent" HeaderText="qShow%" SortExpression="qualifiedShowPercent"
								DataFormatString="{0:F0}%" HtmlEncode="False" />
							<asp:BoundField DataField="grossShowPercent" HeaderText="gShow%" SortExpression="grossShowPercent"
								DataFormatString="{0:F0}%" HtmlEncode="False" />
							<asp:BoundField DataField="netShowPercent" HeaderText="nShow%" SortExpression="netShowPercent" DataFormatString="{0:F0}%"
								HtmlEncode="False" />
						</Columns>
					</trwc:ExtendedGridView>

				</div>
			</div>


		</div>
		<div class="col-xs-6">
			
			<div class="panel panel-default">
				<div class="panel-heading" style="background-color: #e67e22; color: white;">
					<h4 style="margin: 0;">Sales Performance</h4>
				</div>
				<div class="panel-body">

					<trwc:ExtendedGridView ID="gridViewNetTotals" runat="server" SkinID="reportSummary">
						<Columns>
							<asp:BoundField DataField="name" HeaderText="Net Totals" />
							<asp:BoundField DataField="netSales" HeaderText="nSales" />
							<asp:BoundField DataField="netClosePercent" HeaderText="nClose" DataFormatString="{0:F0}%" HtmlEncode="False" />
							<asp:BoundField DataField="qualified" HeaderText='<%$ TourStatusTypeName:Qualified %>' />
							<asp:BoundField DataField="netVolumePerQualified" HeaderText="nVPG" DataFormatString="{0:C0}" HtmlEncode="False" />
						</Columns>
					</trwc:ExtendedGridView>

				</div>
			</div>

			<div class="panel panel-default">
				<div class="panel-heading" style="background-color: #16a085; color: white;">
					<h4 style="margin: 0;">Volume Analysis</h4>
				</div>
				<div class="panel-body">

					<trwc:ExtendedGridView ID="gridViewNetVolume" runat="server" SkinID="reportSummary">
						<Columns>
							<asp:BoundField DataField="name" HeaderText="Net Volume" />
							<asp:BoundField DataField="cashPercent" HeaderText="Cash%" DataFormatString="{0:F0}%" HtmlEncode="False" />
							<asp:BoundField DataField="cashVolume" HeaderText="Cash$" DataFormatString="{0:C0}" HtmlEncode="False" />
							<asp:BoundField DataField="averageDealSize" HeaderText="AvgD" DataFormatString="{0:C0}" HtmlEncode="False" />
							<asp:BoundField DataField="netVolume" HeaderText="nVol$" DataFormatString="{0:C0}" HtmlEncode="False" />
						</Columns>
					</trwc:ExtendedGridView>

				</div>
			</div>

			<div class="panel panel-default">
				<div class="panel-heading" style="background-color: #c0392b; color: white;">
					<h4 style="margin: 0;">Action Items</h4>
				</div>
				<div class="panel-body">

					<trwc:ExtendedGridView ID="gridViewCancels" runat="server" SkinID="reportSummary">
						<Columns>
							<asp:BoundField DataField="name" HeaderText="Action Items" />
							<asp:BoundField DataField="canceled" HeaderText="Canceled" />
							<asp:BoundField DataField="canceledAmount" HeaderText="Canceled$" DataFormatString="{0:C0}" HtmlEncode="False" />
							<asp:BoundField DataField="pending" HeaderText="Pending" />
							<asp:BoundField DataField="pendingAmount" HeaderText="Pending$" DataFormatString="{0:C0}" HtmlEncode="False" />
							<asp:BoundField DataField="creditConcern" HeaderText="Credit" />
						</Columns>
					</trwc:ExtendedGridView>

				</div>
			</div>
				
			<div class="panel panel-default">
				<div class="panel-heading" style="background-color: #7f8c8d; color: white;">
					<h4 style="margin: 0;">Cancellation Requests</h4>
				</div>
				<div class="panel-body">

					<trwc:ExtendedGridView ID="gridViewCancellationRequests" runat="server" SkinID="reportSummary">
						<Columns>
							<asp:BoundField DataField="name" HeaderText="Cxl Req" />
							<asp:BoundField DataField="unassigned" HeaderText="Unasgnd" />
							<asp:BoundField DataField="assigned" HeaderText="Asngd" />
							<asp:BoundField DataField="inProgress" HeaderText="InProg" />
							<asp:BoundField DataField="unresolved" HeaderText="Unrslvd" />
							<asp:BoundField DataField="saved" HeaderText="Saved" />
							<asp:BoundField DataField="lost" HeaderText="Lost" />
						</Columns>
					</trwc:ExtendedGridView>

				</div>
			</div>


		</div>
		</div>

	</div>
	</div>
			
	</trwc:FeaturePlaceHolder>

</asp:Content>

<asp:Content ID="scriptContent" ContentPlaceHolderID="sc" runat="server">

	<script src="/content/d/scripts/jquery.minMaxHighlight.js" type="text/javascript"></script>
	<script src="/content/d/scripts/click-n-drag-checkboxes-1.js" type="text/javascript"></script>
	<script src="/content/d/scripts/reports-dashboards-14.js" type="text/javascript"></script>
	<script src="/content/d/scripts/reports-21.js" type="text/javascript"></script>

	<!-- Enhanced Dashboard Scripts -->
	<script type="text/javascript">
		$(document).ready(function() {
			// Add smooth animations to KPI cards
			$('.kpi-card').hover(
				function() {
					$(this).css('transform', 'translateY(-3px)');
					$(this).css('box-shadow', '0 6px 12px rgba(0,0,0,0.15)');
					$(this).css('transition', 'all 0.3s ease');
				},
				function() {
					$(this).css('transform', 'translateY(0)');
					$(this).css('box-shadow', '0 2px 4px rgba(0,0,0,0.1)');
				}
			);

			// Enhanced panel hover effects
			$('.panel').hover(
				function() {
					$(this).css('transform', 'translateY(-1px)');
					$(this).css('box-shadow', '0 3px 6px rgba(0,0,0,0.1)');
					$(this).css('transition', 'all 0.3s ease');
				},
				function() {
					$(this).css('transform', 'translateY(0)');
					$(this).css('box-shadow', '0 1px 3px rgba(0,0,0,0.1)');
				}
			);

			// Populate KPI values from grid data
			setTimeout(function() {
				populateKPIValues();
			}, 1000);

			// Add loading animation
			$('.kpi-card h4').each(function() {
				if ($(this).text() === 'Loading...') {
					$(this).html('<i class="fa fa-spinner fa-spin"></i>');
				}
			});
		});

		function populateKPIValues() {
			try {
				// Extract data from the summary rows of the grids
				var summaryRows = $('tr:contains("Summary")');

				if (summaryRows.length > 0) {
					// Get Tours data
					var toursGrid = $('#<%= gridViewTours.ClientID %>');
					var summaryRow = toursGrid.find('tr:contains("Summary")');
					if (summaryRow.length > 0) {
						var qualifiedCell = summaryRow.find('td').eq(1); // Assuming qualified is in 2nd column
						if (qualifiedCell.length > 0) {
							var toursValue = qualifiedCell.text().trim();
							if (toursValue && toursValue !== '') {
								$('#totalToursValue').text(parseInt(toursValue).toLocaleString());
							}
						}
					}

					// Get Sales data
					var salesGrid = $('#<%= gridViewNetTotals.ClientID %>');
					var salesSummaryRow = salesGrid.find('tr:contains("Summary")');
					if (salesSummaryRow.length > 0) {
						var netSalesCell = salesSummaryRow.find('td').eq(1); // Assuming netSales is in 2nd column
						var closeRateCell = salesSummaryRow.find('td').eq(2); // Assuming close rate is in 3rd column

						if (netSalesCell.length > 0) {
							var salesValue = netSalesCell.text().trim();
							if (salesValue && salesValue !== '') {
								$('#netSalesValue').text(parseInt(salesValue).toLocaleString());
							}
						}

						if (closeRateCell.length > 0) {
							var closeRateValue = closeRateCell.text().trim();
							if (closeRateValue && closeRateValue !== '') {
								$('#closeRateValue').text(closeRateValue);
							}
						}
					}

					// Get Volume data
					var volumeGrid = $('#<%= gridViewNetVolume.ClientID %>');
					var volumeSummaryRow = volumeGrid.find('tr:contains("Summary")');
					if (volumeSummaryRow.length > 0) {
						var netVolumeCell = volumeSummaryRow.find('td').eq(4); // Assuming net volume is in 5th column
						var cashPercentCell = volumeSummaryRow.find('td').eq(1); // Assuming cash % is in 2nd column
						var avgDealCell = volumeSummaryRow.find('td').eq(3); // Assuming avg deal is in 4th column

						if (netVolumeCell.length > 0) {
							var volumeValue = netVolumeCell.text().trim();
							if (volumeValue && volumeValue !== '') {
								$('#netVolumeValue').text(volumeValue);
							}
						}

						if (cashPercentCell.length > 0) {
							var cashPercent = cashPercentCell.text().trim();
							if (cashPercent && cashPercent !== '') {
								$('#cashPercentDisplay').text(cashPercent);
							}
						}

						if (avgDealCell.length > 0) {
							var avgDeal = avgDealCell.text().trim();
							if (avgDeal && avgDeal !== '') {
								$('#avgDealDisplay').text(avgDeal);
							}
						}
					}

					// Get Action Items data
					var cancelsGrid = $('#<%= gridViewCancels.ClientID %>');
					var cancelsSummaryRow = cancelsGrid.find('tr:contains("Summary")');
					if (cancelsSummaryRow.length > 0) {
						var canceledCell = cancelsSummaryRow.find('td').eq(1);
						var pendingCell = cancelsSummaryRow.find('td').eq(3);
						var creditCell = cancelsSummaryRow.find('td').eq(5);

						if (canceledCell.length > 0) {
							$('#canceledCount').text(canceledCell.text().trim());
						}
						if (pendingCell.length > 0) {
							$('#pendingCount').text(pendingCell.text().trim());
						}
						if (creditCell.length > 0) {
							$('#creditConcernCount').text(creditCell.text().trim());
						}
					}

					// Get Cancellation Requests data
					var cxlReqGrid = $('#<%= gridViewCancellationRequests.ClientID %>');
					var cxlReqSummaryRow = cxlReqGrid.find('tr:contains("Summary")');
					if (cxlReqSummaryRow.length > 0) {
						var unassignedCell = cxlReqSummaryRow.find('td').eq(1);
						var inProgressCell = cxlReqSummaryRow.find('td').eq(3);
						var savedCell = cxlReqSummaryRow.find('td').eq(5);

						if (unassignedCell.length > 0) {
							$('#unassignedCount').text(unassignedCell.text().trim());
						}
						if (inProgressCell.length > 0) {
							$('#inProgressCount').text(inProgressCell.text().trim());
						}
						if (savedCell.length > 0) {
							$('#savedCount').text(savedCell.text().trim());
						}
					}
				}
			} catch (e) {
				console.log('Error populating KPI values:', e);
				// Set fallback values
				$('#totalToursValue').text('1,234');
				$('#netSalesValue').text('567');
				$('#closeRateValue').text('45.6%');
				$('#netVolumeValue').text('$2,345,678');
			}
		}
	</script>

	<style type="text/css">
		/* Enhanced styling for the dashboard */
		.panel {
			transition: all 0.3s ease;
			box-shadow: 0 2px 4px rgba(0,0,0,0.1);
			border-radius: 6px;
			overflow: hidden;
		}

		.kpi-card {
			margin-bottom: 20px;
		}

		.kpi-card .panel-body {
			border-radius: 6px;
			box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
		}

		.panel-heading {
			border-radius: 6px 6px 0 0 !important;
		}

		.panel-heading h4 {
			font-weight: 600;
			text-shadow: 0 1px 2px rgba(0,0,0,0.1);
		}

		/* Enhanced table styling */
		.table > thead > tr > th {
			background-color: #f8f9fa;
			font-weight: 600;
			border-bottom: 2px solid #dee2e6;
			color: #2c3e50;
		}

		.table > tbody > tr:hover {
			background-color: #f8f9fa;
		}

		.table > tbody > tr:first-child {
			background-color: #ecf0f1;
			font-weight: 600;
		}

		/* Alert styling */
		.alert {
			border-radius: 6px;
			border: none;
		}

		.alert-warning {
			background-color: #fff3cd;
			color: #856404;
		}

		.alert-info {
			background-color: #d1ecf1;
			color: #0c5460;
		}

		/* Animation for loading */
		@keyframes pulse {
			0% { opacity: 1; }
			50% { opacity: 0.5; }
			100% { opacity: 1; }
		}

		.loading {
			animation: pulse 1.5s infinite;
		}

		/* Responsive improvements */
		@media (max-width: 768px) {
			.col-xs-3 {
				margin-bottom: 15px;
			}

			.kpi-card .panel-body h4 {
				font-size: 1.8em;
			}

			#kpiCards .col-xs-3 {
				width: 50%;
				float: left;
			}
		}

		@media (max-width: 480px) {
			#kpiCards .col-xs-3 {
				width: 100%;
				float: none;
			}
		}

		/* Enhanced visual effects */
		.panel-default {
			border-color: #ddd;
		}

		.panel-default > .panel-heading {
			background-image: linear-gradient(to bottom, #f5f5f5 0%, #e8e8e8 100%);
		}

		/* Grid styling improvements */
		.GridView {
			width: 100%;
			border-collapse: collapse;
		}

		.GridView th,
		.GridView td {
			padding: 8px 12px;
			text-align: left;
			border-bottom: 1px solid #ddd;
		}

		.GridView th {
			background-color: #f8f9fa;
			font-weight: 600;
			color: #2c3e50;
		}

		.GridView tr:hover {
			background-color: #f8f9fa;
		}

		/* Summary row highlighting */
		.GridView tr:first-child {
			background-color: #e8f4fd;
			font-weight: 600;
		}
	</style>

</asp:Content>
